<script lang="ts">
  import { subjects } from '$lib/constants';
  import type { Score } from '$lib/types';
  import { onMount } from 'svelte';

  export let data: {
    scores: Score[];
    subjects: typeof subjects;
    admin: boolean;
    i: string;
    c: string;
    t: string;
    studentName: string;
  };

  let mounted = false;

  onMount(() => {
    mounted = true;
  });


</script>

<div class="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50/30">
  <div class="max-w-4xl mx-auto px-6 py-12">
    <!-- School Header -->
    <div class="text-center mb-12">
      <img src="/logo.jpg" alt="School Logo" class="w-20 h-20 mx-auto mb-4 object-contain" />
      <h1 class="text-3xl text-slate-900 mb-8" style="font-family: 'Allegro', serif;">
        AngelWings Comprehensive College
      </h1>
    </div>

    <!-- Header Section -->
    <div class="mb-12">
      <div class="text-center mb-8">
        <h2 class="text-4xl font-light tracking-tight text-slate-900 mb-2">
          Midterm Report
        </h2>
        <h3 class="text-2xl font-medium text-slate-700 mb-4">
          {data.studentName}
        </h3>
        <p class="text-lg text-slate-500 font-light">Term {data.t} • Class {data.c}</p>
      </div>

    </div>

    <!-- Scores Table -->
    {#if data.scores.length > 0}
      <div class="bg-white/70 backdrop-blur-sm rounded-3xl shadow-2xl border border-white/20 overflow-hidden">
        <div class="overflow-x-auto">
          <table class="w-full">
            <thead>
              <tr class="border-b border-slate-200/50">
                <th class="text-left py-6 px-8 text-sm font-semibold text-slate-600 uppercase tracking-wide">
                  Subject
                </th>
                <th class="text-right py-6 px-8 text-sm font-semibold text-slate-600 uppercase tracking-wide">
                  1st CA
                </th>
                <th class="text-right py-6 px-8 text-sm font-semibold text-slate-600 uppercase tracking-wide">
                  2nd CA
                </th>
              </tr>
            </thead>
            <tbody>
              {#each data.scores as score, i}
                <tr
                  class="border-b border-slate-100/50 hover:bg-slate-50/50 transition-colors duration-200"
                  class:animate-fade-in={mounted}
                  style="animation-delay: {i * 50}ms"
                >
                  <td class="py-5 px-8">
                    <div class="flex items-center space-x-4">
                      <div class="w-3 h-3 rounded-full bg-gradient-to-r from-blue-400 to-blue-600"></div>
                      <span class="text-slate-900 font-medium">
                        {subjects[score.j as keyof typeof subjects]}
                      </span>
                    </div>
                  </td>
                  <td class="py-5 px-8 text-right">
                    <div class="inline-flex items-center space-x-2">
                      <span class="text-2xl font-light text-slate-900 tabular-nums">
                        {score[1] || '-'}
                      </span>
                      {#if score[1]}
                        <span class="text-sm text-slate-500 font-medium">%</span>
                      {/if}
                    </div>
                  </td>
                  <td class="py-5 px-8 text-right">
                    <div class="inline-flex items-center space-x-2">
                      <span class="text-2xl font-light text-slate-900 tabular-nums">
                        {score[2] || '-'}
                      </span>
                      {#if score[2]}
                        <span class="text-sm text-slate-500 font-medium">%</span>
                      {/if}
                    </div>
                  </td>
                </tr>
              {/each}
            </tbody>
          </table>
        </div>
      </div>
    {:else}
      <!-- Empty State -->
      <div class="text-center py-20">
        <div class="w-24 h-24 mx-auto mb-8 rounded-full bg-gradient-to-br from-slate-100 to-slate-200 flex items-center justify-center">
          <svg class="w-10 h-10 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        </div>
        <h3 class="text-xl font-light text-slate-900 mb-2">No scores yet</h3>
        <p class="text-slate-500 mb-8 max-w-md mx-auto">
          Academic scores for this term haven't been recorded yet.
        </p>
      </div>
    {/if}
  </div>
</div>

<style>
  @keyframes fade-in {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-fade-in {
    animation: fade-in 0.6s ease-out forwards;
    opacity: 0;
  }
</style>